#!/bin/bash
# Final render with gameplay, voiceover, music, and captions

ffmpeg -y \
  -i gameplay_clips/dummy_gameplay.mp4 \
  -i voiceovers/voiceover_sample.mp3 \
  -i music/background_music.mp3 \
  -vf "subtitles=captions/sample_captions.srt:force_style='FontSize=24'" \
  -filter_complex "[1:a][2:a]amix=inputs=2:duration=longest" \
  -map 0:v -map "[a]" -shortest \
  -s 1080x1920 \
  output/final_video.mp4
