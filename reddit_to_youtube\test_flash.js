const gemini = require('./gemini_flash');

async function runDemo() {
  const title = "I got fired for being too honest at work";
  const body = "H<PERSON> told me I couldn't say what I really thought in meetings. So I told them what I thought of that.";

  const score = await gemini.getViralityScore(title, body);
  const tone = await gemini.detectTone(title, body);
  const hook = await gemini.generateHookText(title, body);
  const ytTitle = await gemini.generateYouTubeTitle(title, body);

  console.log({ score, tone, hook, ytTitle });
}

runDemo();