require("dotenv").config();
const { GoogleGenerativeAI } = require("@google/generative-ai");

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "models/gemini-1.5-flash" });

async function askGemini(prompt) {
  const result = await model.generateContent(prompt);
  return result.response.text().trim();
}

async function getViralityScore(title, body) {
  const prompt = `Rate this Reddit post's virality from 0-10. Respond with ONLY the number.
Title: "${title}"
Body: "${body}"`;
  return await askG<PERSON>ini(prompt);
}

async function detectTone(title, body) {
  const prompt = `Is this Reddit post serious or funny? Respond with ONLY "serious" or "funny".
Title: "${title}"
Body: "${body}"`;
  return await askGemini(prompt);
}

async function generateHookText(title, body) {
  const prompt = `Generate ONE short, 5-7 word, highly clickable YouTube thumbnail hook text for this Reddit post. Respond with just the hook text, nothing else.
Title: "${title}"
Body: "${body}"`;
  return await askGemini(prompt);
}

async function generateYouTubeTitle(title, body) {
  const prompt = `Generate ONE catchy YouTube Shorts video title for this Reddit post. Respond with just the title, nothing else.
Title: "${title}"
Body: "${body}"`;
  return await askGemini(prompt);
}

module.exports = {
  getViralityScore,
  detectTone,
  generateHookText,
  generateYouTubeTitle,
};