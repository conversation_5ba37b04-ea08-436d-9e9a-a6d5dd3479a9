{"name": "zod-class", "description": "Create classes from Zod Object schemas all in one line", "version": "0.0.16", "repository": {"url": "https://github.com/sam-goodwin/zod-class"}, "exports": {".": {"import": "./lib/esm/index.js", "require": "./lib/cjs/index.js"}}, "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "files": ["lib"], "scripts": {"build": "tsc -b", "test": "NODE_OPTIONS=--experimental-vm-modules jest && cd ./test/pkg && pnpm i && pnpm build", "watch": "tsc -b -w"}, "dependencies": {"type-fest": "^4.14.0"}, "peerDependencies": {"zod": "^3"}, "devDependencies": {"@tsconfig/node16": "^1.0.3", "@types/jest": "^29.5.4", "@types/node": "^16", "bun": "^1.0.36", "jest": "^29.7.0", "prettier": "^2.8.8", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.4.3", "zod": "3.20.2"}}